<?php

require_once __DIR__ . '/vendor/autoload.php';

$app = require_once __DIR__ . '/bootstrap/app.php';
$app->make(Illuminate\Contracts\Console\Kernel::class)->bootstrap();

echo "=== 测试预警队列系统 ===\n";

// 获取第一个监控任务
$monitoringTask = \App\Models\MonitoringTask::first();
if (!$monitoringTask) {
    echo "未找到监控任务\n";
    exit(1);
}

echo "找到监控任务: {$monitoringTask->name} (ID: {$monitoringTask->id})\n";

// 获取目标产品ID
$targetProducts = $monitoringTask->target_products;
if (empty($targetProducts) || !is_array($targetProducts)) {
    echo "监控任务没有配置目标产品\n";
    exit(1);
}

$firstProduct = $targetProducts[0];
$targetItemId = $firstProduct['product_id'] ?? null;
if (!$targetItemId) {
    echo "无法获取目标产品ID\n";
    exit(1);
}

echo "目标产品ID: {$targetItemId}\n";

// 生成采集批次ID
$collectionBatchId = 'batch_' . date('YmdHis') . '_' . $monitoringTask->id;
echo "生成采集批次ID: {$collectionBatchId}\n";

// 获取数据采集服务
$dataCollectionService = app(\App\Services\DataCollectionService::class);

try {
    // 执行数据采集
    echo "开始执行数据采集...\n";
    $result = $dataCollectionService->collectAndStandardize(
        $monitoringTask->data_source_id,
        $targetItemId,
        [],
        $monitoringTask->id,
        $collectionBatchId
    );
    
    echo "数据采集完成！\n";
    echo "结果: " . json_encode($result, JSON_UNESCAPED_UNICODE | JSON_PRETTY_PRINT) . "\n";
    
} catch (\Exception $e) {
    echo "数据采集失败: " . $e->getMessage() . "\n";
    echo "错误详情: " . $e->getTraceAsString() . "\n";
}

echo "=== 测试完成 ===\n";
