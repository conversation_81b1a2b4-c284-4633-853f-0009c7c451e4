<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('alerts', function (Blueprint $table) {
            $table->foreignId('task_group_alert_summary_id')->nullable()->after('alert_rule_id')->constrained('task_group_alert_summaries')->onDelete('set null')->comment('任务分组预警汇总ID');
            
            // 添加索引
            $table->index('task_group_alert_summary_id');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('alerts', function (Blueprint $table) {
            $table->dropForeign(['task_group_alert_summary_id']);
            $table->dropIndex(['task_group_alert_summary_id']);
            $table->dropColumn('task_group_alert_summary_id');
        });
    }
};
