<?php

namespace App\Services;

use App\Jobs\SendAlertNotification;
use App\Models\Alert;
use App\Models\AlertRule;
use App\Models\ProductData;
use App\Models\MonitoringTask;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Arr;

class AlertService
{
    /**
     * 任务分组预警汇总服务
     */
    private TaskGroupAlertSummaryService $summaryService;

    /**
     * 构造函数
     */
    public function __construct(TaskGroupAlertSummaryService $summaryService)
    {
        $this->summaryService = $summaryService;
    }
    /**
     * 检查并处理产品数据的预警条件
     *
     * @param ProductData $productData
     * @param string|null $collectionBatchId 采集批次ID
     * @return array
     */
    public function processAlerts(ProductData $productData, ?string $collectionBatchId = null): array
    {
        // 如果没有提供采集批次ID，则生成一个
        if (!$collectionBatchId && $productData->monitoring_task_id) {
            $collectionBatchId = $this->summaryService->generateCollectionBatchId($productData->monitoring_task_id);
        }

        Log::info('开始处理产品数据预警', [
            'product_data_id' => $productData->id,
            'item_id' => $productData->item_id,
            'monitoring_task_id' => $productData->monitoring_task_id,
            'collection_batch_id' => $collectionBatchId,
        ]);

        $triggeredAlerts = [];

        try {
            // 获取相关的监控任务
            $monitoringTask = MonitoringTask::find($productData->monitoring_task_id);

            if (!$monitoringTask || $monitoringTask->status !== 'active') {
                 Log::info('未找到激活的监控任务或任务非激活状态，跳过处理。', ['monitoring_task_id' => $productData->monitoring_task_id]);
                 return ['success' => true, 'triggered_alerts' => [], 'alerts_count' => 0];
            }
            
            // 获取关联的预警规则
            $alertRuleIds = $monitoringTask->alert_rule_ids ?? [];
            if (empty($alertRuleIds)) {
                Log::info('监控任务未配置预警规则，跳过处理。', ['monitoring_task_id' => $monitoringTask->id]);
                return ['success' => true, 'triggered_alerts' => [], 'alerts_count' => 0];
            }

            $alertRules = AlertRule::whereIn('id', $alertRuleIds)
                ->where('status', 1) // 1 表示启用
                ->get();

            if ($alertRules->isEmpty()) {
                Log::info('未找到启用的预警规则，跳过处理。', ['alert_rule_ids' => $alertRuleIds]);
                return ['success' => true, 'triggered_alerts' => [], 'alerts_count' => 0];
            }
            
            // 检查任务是否包含当前产品 (如果 target_products 不为空)
            if (!empty($monitoringTask->target_products)) {
                $targetProductIds = $this->extractProductIdsFromTargetProducts($monitoringTask->target_products);
                if (!in_array($productData->item_id, $targetProductIds)) {
                    Log::info('当前产品不在此任务的监控目标中，跳过处理。', [
                        'monitoring_task_id' => $monitoringTask->id,
                        'item_id' => $productData->item_id,
                    ]);
                    return ['success' => true, 'triggered_alerts' => [], 'alerts_count' => 0];
                }
            }
            
            foreach ($alertRules as $rule) {
                $alert = $this->checkAlertRule($rule, $productData, $monitoringTask, $collectionBatchId);
                if ($alert) {
                    $triggeredAlerts[] = $alert;
                }
            }


            Log::info('产品数据预警处理完成', [
                'product_data_id' => $productData->id,
                'triggered_alerts_count' => count($triggeredAlerts),
            ]);

            return [
                'success' => true,
                'triggered_alerts' => $triggeredAlerts,
                'alerts_count' => count($triggeredAlerts),
            ];

        } catch (\Exception $e) {
            Log::error('处理产品数据预警失败', [
                'product_data_id' => $productData->id,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
            ]);

            return [
                'success' => false,
                'error' => $e->getMessage(),
            ];
        }
    }

    /**
     * 检查单个预警规则
     *
     * @param AlertRule $rule
     * @param ProductData $productData
     * @param MonitoringTask $task
     * @param string|null $collectionBatchId
     * @return Alert|null
     */
    private function checkAlertRule(AlertRule $rule, ProductData $productData, MonitoringTask $task, ?string $collectionBatchId = null): ?Alert
    {
        Log::debug('开始检查预警规则', [
            'rule_id' => $rule->id,
            'rule_name' => $rule->name,
            'rule_type' => $rule->rule_type,
            'conditions' => $rule->conditions,
            'product_data_id' => $productData->id,
            'item_id' => $productData->item_id,
        ]);

        $standardizedData = $productData->standardized_data ?? [];
        $triggeredAlerts = [];

        // 检查规则类型数组中的每个类型
        $ruleTypes = is_array($rule->rule_type) ? $rule->rule_type : [$rule->rule_type];
        
        foreach ($ruleTypes as $ruleType) {
            $alert = $this->checkSpecificRuleType($rule, $ruleType, $productData, $task, $standardizedData, $collectionBatchId);
            if ($alert) {
                $triggeredAlerts[] = $alert;
            }
        }

        // 返回第一个触发的预警（如果有多个的话）
        return !empty($triggeredAlerts) ? $triggeredAlerts[0] : null;
    }

    /**
     * 检查特定类型的预警规则
     */
    private function checkSpecificRuleType(AlertRule $rule, string $ruleType, ProductData $productData, MonitoringTask $task, array $standardizedData, ?string $collectionBatchId = null): ?Alert
    {
        switch ($ruleType) {
            case 'promotion_price_deviation':
                return $this->checkPromotionPriceDeviation($rule, $ruleType, $productData, $task, $standardizedData, $collectionBatchId);

            case 'channel_price_deviation':
                return $this->checkChannelPriceDeviation($rule, $ruleType, $productData, $task, $standardizedData, $collectionBatchId);

            case 'listing_status_change':
                return $this->checkListingStatusChange($rule, $ruleType, $productData, $task, $standardizedData, $collectionBatchId);

            default:
                Log::warning('未知的预警规则类型', ['rule_type' => $ruleType, 'rule_id' => $rule->id]);
                return null;
        }
    }

    /**
     * 检查促销价偏离率预警
     */
    private function checkPromotionPriceDeviation(AlertRule $rule, string $ruleType, ProductData $productData, MonitoringTask $task, array $standardizedData, ?string $collectionBatchId = null): ?Alert
    {
        $conditions = $rule->conditions[$ruleType] ?? null;
        if (!$conditions || !isset($conditions['operator']) || !isset($conditions['threshold'])) {
            Log::debug('促销价偏离率预警条件配置不完整', ['rule_id' => $rule->id, 'conditions' => $conditions]);
            return null;
        }

        $operator = $conditions['operator'];
        $threshold = (float)$conditions['threshold'];

        // 检查商品的SKU数据
        $skus = $productData->skus;
        if ($skus->isEmpty()) {
            // 如果没有SKU，检查商品级别的数据
            return $this->checkSingleProductPromotionDeviation($rule, $ruleType, $productData, $task, $standardizedData, $operator, $threshold, $collectionBatchId);
        }

        // 检查每个SKU的促销价偏离率
        foreach ($skus as $sku) {
            $deviationRate = $this->calculateSkuPromotionDeviation($sku);
            if ($deviationRate !== null && $this->compareNumeric($deviationRate, $operator, $threshold)) {
                return $this->createAlert($rule, $productData, $task, $deviationRate, $ruleType, "SKU {$sku->sku_id} 促销价偏离率", $collectionBatchId);
            }
        }

        return null;
    }

    /**
     * 检查渠道价格偏离率预警
     */
    private function checkChannelPriceDeviation(AlertRule $rule, string $ruleType, ProductData $productData, MonitoringTask $task, array $standardizedData, ?string $collectionBatchId = null): ?Alert
    {
        $conditions = $rule->conditions[$ruleType] ?? null;
        if (!$conditions || !isset($conditions['operator']) || !isset($conditions['threshold'])) {
            Log::debug('渠道价格偏离率预警条件配置不完整', ['rule_id' => $rule->id, 'conditions' => $conditions]);
            return null;
        }

        $operator = $conditions['operator'];
        $threshold = (float)$conditions['threshold'];

        // 获取监控任务中的官方指导价配置
        $officialGuidePrice = $this->getOfficialGuidePriceForProduct($task, $productData->item_id);
        if (!$officialGuidePrice) {
            Log::debug('未找到商品的官方指导价配置', ['item_id' => $productData->item_id, 'task_id' => $task->id]);
            return null;
        }

        // 检查商品的SKU数据
        $skus = $productData->skus;
        if ($skus->isEmpty()) {
            // 如果没有SKU，检查商品级别的数据
            return $this->checkSingleProductChannelDeviation($rule, $ruleType, $productData, $task, $standardizedData, $operator, $threshold, $officialGuidePrice, $collectionBatchId);
        }

        // 检查每个SKU的渠道价格偏离率
        foreach ($skus as $sku) {
            $deviationRate = $this->calculateSkuChannelDeviation($sku, $officialGuidePrice);
            if ($deviationRate !== null && $this->compareNumeric($deviationRate, $operator, $threshold)) {
                return $this->createAlert($rule, $productData, $task, $deviationRate, $ruleType, "SKU {$sku->sku_id} 渠道价格偏离率", $collectionBatchId);
            }
        }

        return null;
    }

    /**
     * 检查上下架状态变更预警
     */
    private function checkListingStatusChange(AlertRule $rule, string $ruleType, ProductData $productData, MonitoringTask $task, array $standardizedData, ?string $collectionBatchId = null): ?Alert
    {
        // 获取当前状态
        $currentState = $productData->state;
        
        // 获取历史数据进行对比
        $previousData = ProductData::where('monitoring_task_id', $productData->monitoring_task_id)
            ->where('item_id', $productData->item_id)
            ->where('id', '<', $productData->id)
            ->orderBy('created_at', 'desc')
            ->first();

        if (!$previousData) {
            Log::debug('未找到历史数据，跳过上下架状态检查', ['product_data_id' => $productData->id]);
            return null;
        }

        $previousState = $previousData->state;

        // 检查是否发生了下架事件（从上架变为下架）
        if ($previousState == 1 && $currentState == 0) {
            return $this->createAlert($rule, $productData, $task, '下架', $ruleType, '商品状态变更', $collectionBatchId);
        }

        return null;
    }

    /**
     * 计算SKU促销价偏离率
     */
    private function calculateSkuPromotionDeviation($sku): ?float
    {
        if (!$sku->price || $sku->price <= 0) {
            return null;
        }

        $subPrice = $sku->sub_price ?? $sku->price;
        return round((($sku->price - $subPrice) / $sku->price) * 100, 2);
    }

    /**
     * 计算SKU渠道价格偏离率
     */
    private function calculateSkuChannelDeviation($sku, float $officialGuidePrice): ?float
    {
        if ($officialGuidePrice <= 0) {
            return null;
        }

        $subPrice = $sku->sub_price ?? $sku->price;
        if (!$subPrice || $subPrice <= 0) {
            $subPrice = $sku->price ?? 0;
        }

        if ($subPrice <= 0) {
            return null;
        }

        return round((($officialGuidePrice - $subPrice) / $officialGuidePrice) * 100, 2);
    }

    /**
     * 检查单商品促销价偏离率（无SKU情况）
     */
    private function checkSingleProductPromotionDeviation(AlertRule $rule, string $ruleType, ProductData $productData, MonitoringTask $task, array $standardizedData, string $operator, float $threshold, ?string $collectionBatchId = null): ?Alert
    {
        $price = $standardizedData['Price'] ?? $productData->price ?? 0;
        $subPrice = $standardizedData['subPrice'] ?? $standardizedData['sub_price'] ?? $price;

        if ($price <= 0) {
            return null;
        }

        $deviationRate = round((($price - $subPrice) / $price) * 100, 2);
        
        if ($this->compareNumeric($deviationRate, $operator, $threshold)) {
            return $this->createAlert($rule, $productData, $task, $deviationRate, $ruleType, '商品促销价偏离率', $collectionBatchId);
        }

        return null;
    }

    /**
     * 检查单商品渠道价格偏离率（无SKU情况）
     */
    private function checkSingleProductChannelDeviation(AlertRule $rule, string $ruleType, ProductData $productData, MonitoringTask $task, array $standardizedData, string $operator, float $threshold, float $officialGuidePrice, ?string $collectionBatchId = null): ?Alert
    {
        $subPrice = $standardizedData['subPrice'] ?? $standardizedData['sub_price'] ?? $productData->price ?? 0;

        if ($subPrice <= 0) {
            return null;
        }

        $deviationRate = round((($officialGuidePrice - $subPrice) / $officialGuidePrice) * 100, 2);
        
        if ($this->compareNumeric($deviationRate, $operator, $threshold)) {
            return $this->createAlert($rule, $productData, $task, $deviationRate, $ruleType, '商品渠道价格偏离率', $collectionBatchId);
        }

        return null;
    }

    /**
     * 获取商品的官方指导价
     */
    private function getOfficialGuidePriceForProduct(MonitoringTask $task, string $itemId): ?float
    {
        $targetProducts = $task->target_products ?? [];
        
        // 如果target_products是新格式（包含official_guide_price的对象数组）
        if (!empty($targetProducts) && is_array($targetProducts)) {
            foreach ($targetProducts as $product) {
                if (is_array($product) && 
                    isset($product['product_id']) && 
                    $product['product_id'] == $itemId &&
                    isset($product['official_guide_price'])) {
                    return (float)$product['official_guide_price'];
                }
            }
        }

        return null;
    }

    /**
     * 从target_products配置中提取商品ID列表
     */
    private function extractProductIdsFromTargetProducts(array $targetProducts): array
    {
        $productIds = [];
        
        foreach ($targetProducts as $product) {
            if (is_string($product)) {
                // 旧格式：直接是商品ID字符串
                $productIds[] = $product;
            } elseif (is_array($product) && isset($product['product_id'])) {
                // 新格式：包含product_id和其他信息的对象
                $productIds[] = $product['product_id'];
            }
        }
        
        return $productIds;
    }

    /**
     * 数值比较
     */
    private function compareNumeric($value, string $operator, $threshold): bool
    {
        if (!is_numeric($value) || !is_numeric($threshold)) {
            return false;
        }

        $value = (float)$value;
        $threshold = (float)$threshold;

        switch ($operator) {
            case '>':
                return $value > $threshold;
            case '<':
                return $value < $threshold;
            case '>=':
                return $value >= $threshold;
            case '<=':
                return $value <= $threshold;
            default:
                return false;
        }
    }

    /**
     * 检查值是否在范围内
     */
    private function isBetween($value, $min, $max): bool
    {
        if (!is_numeric($value) || !is_numeric($min) || !is_numeric($max)) {
            return false;
        }

        $value = (float)$value;
        $min = (float)$min;
        $max = (float)$max;

        return $value >= $min && $value <= $max;
    }



    /**
     * 创建预警记录
     */
    private function createAlert(AlertRule $rule, ProductData $productData, MonitoringTask $task, $targetValue, string $ruleType = null, string $targetField = null, ?string $collectionBatchId = null): Alert
    {
        $message = $this->generateAlertMessage($rule, $productData, $targetValue, $ruleType, $targetField);

        $conditions = $ruleType ? ($rule->conditions[$ruleType] ?? []) : [];

        $alert = Alert::create([
            'monitoring_task_id' => $task->id,
            'alert_rule_id' => $rule->id,
            'user_id' => $rule->user_id,
            'product_id' => $productData->item_id, // 使用item_id而不是数据库ID
            'sku_id' => $this->extractSkuIdFromTargetField($targetField), // 提取SKU ID
            'title' => $rule->name,
            'message' => $message,
            'severity' => $rule->severity,
            'priority' => $rule->priority,
            'trigger_field' => $targetField ?? $ruleType,
            'trigger_value' => (string)$targetValue,
            'threshold_value' => (string)($conditions['threshold'] ?? ''),
            'operator' => $conditions['operator'] ?? '',
            'product_title' => $productData->title, // 添加商品标题
            'trigger_data' => [
                'target_field' => $targetField ?? $ruleType,
                'target_value' => $targetValue,
                'operator' => $conditions['operator'] ?? null,
                'threshold_values' => isset($conditions['threshold']) ? [$conditions['threshold']] : [],
                'rule_type' => $ruleType,
                'product_data' => [
                    'item_id' => $productData->item_id,
                    'data_source' => $productData->monitoringTask?->dataSource?->name ?? '未知',
                    'standardized_data' => $productData->standardized_data,
                ],
            ],
            'status' => 'pending', // 未处理
        ]);

        // 更新规则触发统计
        $this->updateRuleStatistics($rule);

        // 将预警添加到任务分组汇总中（如果提供了采集批次ID）
        if ($collectionBatchId) {
            $this->summaryService->addAlertToSummary($alert, $collectionBatchId);

            Log::info('预警已添加到任务分组汇总', [
                'alert_id' => $alert->id,
                'collection_batch_id' => $collectionBatchId,
            ]);
        } else {
            // 如果没有采集批次ID，则使用原有的单独通知方式
            $this->dispatchNotification($alert);

            Log::info('预警使用单独通知方式', [
                'alert_id' => $alert->id,
            ]);
        }

        Log::info('预警规则触发', [
            'rule_id' => $rule->id,
            'rule_name' => $rule->name,
            'alert_id' => $alert->id,
            'target_value' => $targetValue,
            'rule_type' => $ruleType,
            'target_field' => $targetField,
            'collection_batch_id' => $collectionBatchId,
        ]);

        return $alert;
    }

    /**
     * 完成任务分组的数据采集并触发汇总通知
     *
     * @param int $monitoringTaskId
     * @param string $collectionBatchId
     * @return void
     */
    public function completeTaskGroupCollection(int $monitoringTaskId, string $collectionBatchId): void
    {
        Log::info('完成任务分组数据采集', [
            'monitoring_task_id' => $monitoringTaskId,
            'collection_batch_id' => $collectionBatchId,
        ]);

        try {
            $this->summaryService->finalizeSummary($monitoringTaskId, $collectionBatchId);
        } catch (\Exception $e) {
            Log::error('完成任务分组数据采集失败', [
                'monitoring_task_id' => $monitoringTaskId,
                'collection_batch_id' => $collectionBatchId,
                'error' => $e->getMessage(),
            ]);
        }
    }

    /**
     * 生成预警消息
     */
    private function generateAlertMessage(AlertRule $rule, ProductData $productData, $targetValue, string $ruleType = null, string $targetField = null): string
    {
        $standardizedData = $productData->standardized_data ?? [];
        $productTitle = $standardizedData['title'] ?? $standardizedData['Title'] ?? "商品ID: {$productData->item_id}";
        
        $conditions = $ruleType ? ($rule->conditions[$ruleType] ?? []) : [];
        $operator = $conditions['operator'] ?? '';
        $threshold = $conditions['threshold'] ?? '';

        $fieldDisplayName = $this->getFieldDisplayName($ruleType ?? $targetField);

        return sprintf(
            '产品 "%s" 的 %s 当前值为 %s，触发了预警条件 %s %s',
            $productTitle,
            $fieldDisplayName,
            is_numeric($targetValue) ? number_format($targetValue, 2) . '%' : $targetValue,
            $operator,
            is_numeric($threshold) ? number_format($threshold, 2) . '%' : $threshold
        );
    }

    /**
     * 获取字段显示名称
     */
    private function getFieldDisplayName(?string $field): string
    {
        $fieldNames = [
            'promotion_price_deviation' => '促销价偏离率',
            'channel_price_deviation' => '渠道价格偏离率',
            'listing_status_change' => '上下架状态',
        ];

        return $fieldNames[$field] ?? $field ?? '未知字段';
    }

    /**
     * 更新规则触发统计
     */
    private function updateRuleStatistics(AlertRule $rule): void
    {
        // 由于AlertRule表中可能没有trigger_count字段，我们只更新last_triggered_at
        $rule->update([
            'last_triggered_at' => now(),
        ]);
    }

    /**
     * 分发通知发送任务到队列
     *
     * @param Alert $alert
     * @return void
     */
    private function dispatchNotification(Alert $alert): void
    {
        try {
            // 检查是否配置了通知渠道
            $notificationChannels = $alert->alertRule->notification_method ?? [];
            
            if (empty($notificationChannels)) {
                Log::debug('预警规则未配置通知渠道，跳过通知发送', [
                    'alert_id' => $alert->id,
                    'alert_rule_id' => $alert->alert_rule_id,
                ]);
                return;
            }

            SendAlertNotification::dispatch($alert->id)
                ->onQueue('notifications') // 使用专门的队列处理通知
                ->delay(now()->addSeconds(2)); // 延迟2秒执行，确保预警记录已完全保存

            Log::debug('预警通知任务已分发到队列', [
                'alert_id' => $alert->id,
                'notification_channels' => $notificationChannels,
            ]);
        } catch (\Exception $e) {
            Log::error('分发预警通知任务失败', [
                'alert_id' => $alert->id,
                'error' => $e->getMessage(),
            ]);
        }
    }

    /**
     * 从目标字段中提取SKU ID
     */
    private function extractSkuIdFromTargetField(?string $targetField): ?string
    {
        if (!$targetField) {
            return null;
        }
        
        // 匹配 "SKU SKU001 促销价偏离率" 格式中的SKU ID
        if (preg_match('/SKU\s+(\S+)\s+/', $targetField, $matches)) {
            return $matches[1];
        }
        
        return null;
    }
} 